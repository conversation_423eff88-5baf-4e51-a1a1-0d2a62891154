# 🚀 Gemini Pro 配置指南

本指南将帮你配置和使用Google Gemini Pro模型进行心理健康内容分析。

## 📋 前提条件

### 1. Google AI Studio账号
- 访问 [Google AI Studio](https://makersuite.google.com/)
- 使用Google账号登录
- 确保你有付费的Gemini Pro访问权限

### 2. API密钥获取
1. 在Google AI Studio中，点击左侧菜单的 "Get API key"
2. 点击 "Create API key in new project" 或选择现有项目
3. 复制生成的API密钥

## ⚙️ 配置步骤

### 1. 环境变量配置

编辑你的 `.env` 文件：

```env
# AI Provider Configuration
AI_PROVIDER=gemini
GEMINI_API_KEY=your-actual-gemini-api-key-here
GEMINI_MODEL=gemini-1.5-pro

# 其他必需配置...
REDDIT_CLIENT_ID=your-reddit-client-id
REDDIT_CLIENT_SECRET=your-reddit-client-secret
YOUTUBE_API_KEY=your-youtube-api-key
```

### 2. 模型选择

你可以选择以下Gemini模型：

| 模型 | 特点 | 费用 | 推荐用途 |
|------|------|------|----------|
| `gemini-1.5-flash` | 快速、免费额度 | 免费 (有限制) | 开发测试 |
| `gemini-1.5-pro` | 高质量、更强推理 | 付费 | 生产环境 |

### 3. Pro模型优势

使用Gemini Pro相比Flash模型的优势：

- ✅ **更高质量分析**: 更准确的情感识别和故事分类
- ✅ **更大上下文**: 支持更长的文本内容分析
- ✅ **更高速率限制**: 每分钟60个请求 vs 15个请求
- ✅ **更大批处理**: 批处理大小5 vs 3
- ✅ **更多输出tokens**: 2048 vs 1000 tokens
- ✅ **更强推理能力**: 复杂心理健康内容的深度理解

## 🧪 测试配置

### 1. 基础测试
```bash
cd mental-health-backend
python test_gemini_pro.py
```

### 2. 性能对比测试
```bash
# 测试不同模型性能
python test_gemini_pro.py
```

### 3. 预期输出示例

成功配置后，你应该看到类似输出：

```
🚀 Testing Gemini Pro AI Analyzer...
✨ Pro model detected - Enhanced capabilities enabled!
✅ Initialized gemini analyzer

📋 Provider Information:
   Model: gemini-1.5-pro
   Tier: pro
   Rate limits: 60/min
   Batch size: 5
   Max tokens: 2048

🎯 Classification Results:
   Primary category: bipolar_management
   Confidence score: 0.92
   Key themes: medication, therapy, support_system, recovery, hope

💭 Sentiment Analysis Results:
   Overall sentiment: positive
   Hope score: 0.85
   Recovery stage: maintaining
   Shows progress: True
```

## 💰 费用估算

### Gemini Pro定价 (2024年)
- **输入**: $0.00125 per 1K tokens
- **输出**: $0.005 per 1K tokens

### 使用估算
假设每天分析1000条内容，平均每条500 tokens：

- **每日成本**: ~$3-5 USD
- **每月成本**: ~$90-150 USD
- **相比OpenAI GPT-4**: 约便宜60-70%

## 🔧 高级配置

### 1. 自定义生成参数

你可以在代码中调整Gemini的生成参数：

```python
# 在 gemini_analyzer.py 中
self.generation_config = genai.types.GenerationConfig(
    temperature=0.3,        # 创造性 (0.0-1.0)
    max_output_tokens=2048, # 最大输出长度
    top_p=0.8,             # 核采样
    top_k=40,              # Top-K采样
)
```

### 2. 安全设置

Gemini支持内容安全过滤：

```python
safety_settings = [
    {
        "category": "HARM_CATEGORY_HARASSMENT",
        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
    },
    {
        "category": "HARM_CATEGORY_HATE_SPEECH", 
        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
    }
]
```

### 3. 批处理优化

Pro模型支持更大的批处理：

```python
# Pro模型配置
batch_size = 5
rate_limit_delay = 0.5  # 更快的请求间隔
```

## 🚨 故障排除

### 常见问题

1. **API密钥错误**
   ```
   Error: 401 Unauthorized
   ```
   - 检查API密钥是否正确
   - 确认API密钥有Gemini Pro访问权限

2. **配额超限**
   ```
   Error: 429 Too Many Requests
   ```
   - 检查你的付费计划
   - 调整批处理大小和请求频率

3. **模型不可用**
   ```
   Error: Model not found
   ```
   - 确认你有Gemini Pro访问权限
   - 检查模型名称拼写

### 调试技巧

1. **启用详细日志**:
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **检查配置**:
   ```bash
   python -c "from app.core.config import settings; print(f'Model: {settings.GEMINI_MODEL}')"
   ```

3. **测试API连接**:
   ```python
   import google.generativeai as genai
   genai.configure(api_key="your-api-key")
   model = genai.GenerativeModel('gemini-1.5-pro')
   response = model.generate_content("Hello")
   print(response.text)
   ```

## 📊 监控和优化

### 1. 性能监控

监控以下指标：
- 请求延迟
- 成功率
- Token使用量
- 费用

### 2. 成本优化建议

- 使用Flash模型进行开发测试
- 批处理请求以提高效率
- 缓存常见分析结果
- 设置合理的内容长度限制

## 🔄 从OpenAI迁移

如果你之前使用OpenAI，迁移到Gemini Pro很简单：

1. 更新环境变量：
   ```env
   AI_PROVIDER=gemini
   GEMINI_MODEL=gemini-1.5-pro
   ```

2. 代码无需修改 - 统一接口自动处理

3. 测试功能确保一切正常

## 📞 支持

如果遇到问题：

1. 查看 [Google AI Studio文档](https://ai.google.dev/)
2. 检查 [Gemini API参考](https://ai.google.dev/api)
3. 在项目中提交Issue

---

配置完成后，你就可以享受Gemini Pro强大的AI分析能力了！🎉
