"""
AutoGen 编程工作流
使用最新的 AutoGen 框架实现三个 agent 的编程工作流：
- Agent1: 代码编写者 (CodeWriter)
- Agent2: 代码审查者 (CodeReviewer) 
- Agent3: 代码优化者 (CodeOptimizer)
"""

import asyncio
import re
from typing import List, Optional
from dataclasses import dataclass

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import DiGraphBuilder, GraphFlow
from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient


@dataclass
class CodeTask:
    """代码任务数据结构"""
    description: str
    requirements: List[str]
    language: str = "python"


@dataclass
class CodeReview:
    """代码审查结果数据结构"""
    code: str
    issues: List[str]
    suggestions: List[str]
    approved: bool


class ProgrammingWorkflow:
    """编程工作流主类"""
    
    def __init__(self, model_name: str = "gpt-4o", api_key: Optional[str] = None):
        """
        初始化编程工作流
        
        Args:
            model_name: 使用的模型名称
            api_key: OpenAI API 密钥
        """
        self.model_client = OpenAIChatCompletionClient(
            model=model_name,
            api_key=api_key
        )
        self._setup_agents()
        self._setup_workflow()
    
    def _setup_agents(self):
        """设置三个 agent"""
        
        # Agent1: 代码编写者
        self.code_writer = AssistantAgent(
            name="CodeWriter",
            model_client=self.model_client,
            system_message="""你是一个专业的代码编写者。你的任务是：
1. 根据用户需求编写高质量的代码
2. 确保代码具有良好的结构和可读性
3. 添加必要的注释和文档字符串
4. 遵循最佳编程实践
5. 将代码包装在markdown代码块中

请始终提供完整、可运行的代码解决方案。"""
        )
        
        # Agent2: 代码审查者
        self.code_reviewer = AssistantAgent(
            name="CodeReviewer", 
            model_client=self.model_client,
            system_message="""你是一个经验丰富的代码审查者。你的任务是：
1. 仔细审查提供的代码
2. 识别潜在的bug、性能问题和安全漏洞
3. 检查代码风格和最佳实践的遵循情况
4. 提供具体的改进建议
5. 评估代码的可维护性和可扩展性

请提供详细的审查报告，包括：
- 发现的问题列表
- 具体的改进建议
- 总体评价（APPROVED 或 NEEDS_IMPROVEMENT）

如果代码质量良好，请在回复末尾添加 "APPROVED"。
如果需要改进，请在回复末尾添加 "NEEDS_IMPROVEMENT"。"""
        )
        
        # Agent3: 代码优化者
        self.code_optimizer = AssistantAgent(
            name="CodeOptimizer",
            model_client=self.model_client, 
            system_message="""你是一个代码优化专家。你的任务是：
1. 基于原始代码和审查建议进行优化
2. 提升代码性能、可读性和可维护性
3. 修复审查中发现的问题
4. 应用最佳编程实践和设计模式
5. 确保优化后的代码功能完整

请提供：
1. 优化后的完整代码（包装在markdown代码块中）
2. 优化说明，解释所做的改进
3. 性能或质量提升的预期效果

在完成优化后，请在回复末尾添加 "OPTIMIZATION_COMPLETE"。"""
        )
    
    def _setup_workflow(self):
        """设置工作流图"""
        builder = DiGraphBuilder()
        
        # 添加节点
        builder.add_node(self.code_writer)
        builder.add_node(self.code_reviewer) 
        builder.add_node(self.code_optimizer)
        
        # 定义工作流边
        # CodeWriter -> CodeReviewer
        builder.add_edge(self.code_writer, self.code_reviewer)
        
        # CodeReviewer -> CodeOptimizer (当需要改进时)
        builder.add_edge(
            self.code_reviewer, 
            self.code_optimizer,
            condition=lambda msg: "NEEDS_IMPROVEMENT" in msg.content or "APPROVED" in msg.content
        )
        
        # 设置入口点
        builder.set_entry_point(self.code_writer)
        
        # 构建图
        self.graph = builder.build()
        
        # 设置终止条件
        self.termination_condition = (
            TextMentionTermination("OPTIMIZATION_COMPLETE") | 
            MaxMessageTermination(10)
        )
        
        # 创建工作流
        self.workflow = GraphFlow(
            participants=[self.code_writer, self.code_reviewer, self.code_optimizer],
            graph=self.graph,
            termination_condition=self.termination_condition
        )
    
    async def run_programming_task(self, task_description: str) -> None:
        """
        运行编程任务
        
        Args:
            task_description: 编程任务描述
        """
        print(f"🚀 开始编程工作流")
        print(f"📝 任务描述: {task_description}")
        print("=" * 80)
        
        # 运行工作流
        result = await Console(self.workflow.run_stream(task=task_description))
        
        print("=" * 80)
        print("✅ 编程工作流完成")
        
        return result
    
    async def close(self):
        """关闭模型客户端"""
        await self.model_client.close()


async def main():
    """主函数示例"""
    # 创建工作流实例
    workflow = ProgrammingWorkflow()
    
    try:
        # 示例任务
        task = """
        请编写一个Python函数，实现以下功能：
        1. 接收一个整数列表作为输入
        2. 计算列表中所有偶数的平方和
        3. 返回结果
        4. 包含错误处理和输入验证
        5. 添加完整的文档字符串和类型注解
        """
        
        # 运行工作流
        await workflow.run_programming_task(task)
        
    finally:
        # 关闭资源
        await workflow.close()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
