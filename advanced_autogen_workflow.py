"""
高级 AutoGen 编程工作流
包含代码执行、测试生成和多轮优化功能
"""

import asyncio
import json
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime

from autogen_agentchat.agents import AssistantAgent, CodeExecutorAgent
from autogen_agentchat.teams import DiGraphBuilder, Graph<PERSON>low
from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor


@dataclass
class WorkflowConfig:
    """工作流配置"""
    model_name: str = "gpt-4o"
    max_iterations: int = 3
    enable_code_execution: bool = True
    enable_testing: bool = True
    output_dir: str = "workflow_output"
    temperature: float = 0.1


@dataclass
class TaskResult:
    """任务结果"""
    task_id: str
    description: str
    final_code: str
    review_history: List[str]
    optimization_history: List[str]
    execution_results: List[str]
    timestamp: str
    success: bool


class AdvancedProgrammingWorkflow:
    """高级编程工作流"""
    
    def __init__(self, config: WorkflowConfig):
        self.config = config
        self.model_client = OpenAIChatCompletionClient(
            model=config.model_name,
            temperature=config.temperature
        )
        
        # 创建输出目录
        os.makedirs(config.output_dir, exist_ok=True)
        
        # 设置代码执行器
        if config.enable_code_execution:
            self.code_executor = LocalCommandLineCodeExecutor(
                work_dir=os.path.join(config.output_dir, "execution")
            )
        
        self._setup_agents()
        self._setup_workflow()
    
    def _setup_agents(self):
        """设置所有 agent"""
        
        # 代码编写者 - 增强版
        self.code_writer = AssistantAgent(
            name="CodeWriter",
            model_client=self.model_client,
            system_message="""你是一个资深的软件工程师和代码编写专家。

核心职责：
1. 分析用户需求，设计合适的解决方案
2. 编写高质量、可维护的代码
3. 遵循SOLID原则和最佳编程实践
4. 提供完整的类型注解和文档字符串
5. 考虑边界情况和错误处理

代码标准：
- 使用清晰的变量和函数命名
- 添加适当的注释解释复杂逻辑
- 包含完整的docstring
- 遵循PEP 8风格指南（Python）
- 提供使用示例

输出格式：
- 将代码包装在```python代码块中
- 在代码前提供简要的设计说明
- 在代码后说明关键特性和使用方法"""
        )
        
        # 代码审查者 - 增强版
        self.code_reviewer = AssistantAgent(
            name="CodeReviewer",
            model_client=self.model_client,
            system_message="""你是一个经验丰富的代码审查专家和架构师。

审查维度：
1. 功能正确性 - 代码是否满足需求
2. 代码质量 - 可读性、可维护性、可扩展性
3. 性能考虑 - 时间复杂度、空间复杂度
4. 安全性 - 潜在的安全漏洞
5. 最佳实践 - 设计模式、编程规范
6. 测试覆盖 - 是否需要额外的测试用例

审查流程：
1. 逐行分析代码逻辑
2. 识别潜在问题和改进点
3. 提供具体的修改建议
4. 评估整体代码质量

输出格式：
✅ 优点：列出代码的优秀之处
⚠️ 问题：详细描述发现的问题
💡 建议：提供具体的改进建议
📊 评分：给出1-10分的质量评分

最后必须明确表态：
- 如果代码质量良好：回复末尾添加 "APPROVED"
- 如果需要改进：回复末尾添加 "NEEDS_IMPROVEMENT"
- 如果需要重写：回复末尾添加 "NEEDS_REWRITE" """
        )
        
        # 代码优化者 - 增强版
        self.code_optimizer = AssistantAgent(
            name="CodeOptimizer", 
            model_client=self.model_client,
            system_message="""你是一个代码优化和重构专家。

优化策略：
1. 性能优化 - 算法优化、数据结构选择
2. 代码重构 - 提取函数、消除重复
3. 可读性提升 - 改善命名、简化逻辑
4. 架构改进 - 应用设计模式、分离关注点
5. 错误处理 - 完善异常处理机制
6. 文档完善 - 改进注释和文档字符串

优化原则：
- 保持功能完整性
- 提升代码可维护性
- 优化性能表现
- 增强错误处理
- 改善用户体验

输出格式：
🔧 优化说明：解释优化策略和理由
```python
# 优化后的完整代码
```
📈 改进效果：说明预期的性能和质量提升
🧪 测试建议：推荐的测试用例

完成后在回复末尾添加 "OPTIMIZATION_COMPLETE" """
        )
        
        # 代码执行者（如果启用）
        if self.config.enable_code_execution:
            self.code_executor_agent = CodeExecutorAgent(
                name="CodeExecutor",
                code_executor=self.code_executor
            )
    
    def _setup_workflow(self):
        """设置工作流图"""
        builder = DiGraphBuilder()
        
        # 添加核心节点
        builder.add_node(self.code_writer)
        builder.add_node(self.code_reviewer)
        builder.add_node(self.code_optimizer)
        
        # 如果启用代码执行，添加执行节点
        if self.config.enable_code_execution:
            builder.add_node(self.code_executor_agent)
        
        # 定义工作流边
        builder.add_edge(self.code_writer, self.code_reviewer)
        
        # 审查通过 -> 优化
        builder.add_edge(
            self.code_reviewer,
            self.code_optimizer,
            condition=lambda msg: any(keyword in msg.content for keyword in 
                                    ["APPROVED", "NEEDS_IMPROVEMENT", "NEEDS_REWRITE"])
        )
        
        # 如果启用代码执行
        if self.config.enable_code_execution:
            builder.add_edge(self.code_optimizer, self.code_executor_agent)
        
        # 设置入口点
        builder.set_entry_point(self.code_writer)
        
        # 构建图
        self.graph = builder.build()
        
        # 设置终止条件
        self.termination_condition = (
            TextMentionTermination("OPTIMIZATION_COMPLETE") |
            MaxMessageTermination(self.config.max_iterations * 3)
        )
        
        # 创建工作流
        participants = [self.code_writer, self.code_reviewer, self.code_optimizer]
        if self.config.enable_code_execution:
            participants.append(self.code_executor_agent)
            
        self.workflow = GraphFlow(
            participants=participants,
            graph=self.graph,
            termination_condition=self.termination_condition
        )
    
    async def run_task(self, task_description: str, task_id: Optional[str] = None) -> TaskResult:
        """运行编程任务"""
        if task_id is None:
            task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        print(f"🚀 启动高级编程工作流")
        print(f"📋 任务ID: {task_id}")
        print(f"📝 任务描述: {task_description}")
        print("=" * 100)
        
        # 运行工作流
        result = await Console(self.workflow.run_stream(task=task_description))
        
        # 处理结果
        task_result = self._process_result(task_id, task_description, result)
        
        # 保存结果
        await self._save_result(task_result)
        
        print("=" * 100)
        print(f"✅ 工作流完成 - 任务ID: {task_id}")
        
        return task_result
    
    def _process_result(self, task_id: str, description: str, result) -> TaskResult:
        """处理工作流结果"""
        messages = result.messages if hasattr(result, 'messages') else []
        
        final_code = ""
        review_history = []
        optimization_history = []
        execution_results = []
        
        for msg in messages:
            content = msg.content if hasattr(msg, 'content') else str(msg)
            source = msg.source if hasattr(msg, 'source') else "unknown"
            
            if source == "CodeReviewer":
                review_history.append(content)
            elif source == "CodeOptimizer":
                optimization_history.append(content)
                # 提取最终代码
                if "```python" in content:
                    final_code = self._extract_code(content)
            elif source == "CodeExecutor":
                execution_results.append(content)
        
        return TaskResult(
            task_id=task_id,
            description=description,
            final_code=final_code,
            review_history=review_history,
            optimization_history=optimization_history,
            execution_results=execution_results,
            timestamp=datetime.now().isoformat(),
            success=bool(final_code and "OPTIMIZATION_COMPLETE" in str(messages))
        )
    
    def _extract_code(self, content: str) -> str:
        """从消息中提取代码"""
        import re
        pattern = r"```python\n(.*?)\n```"
        match = re.search(pattern, content, re.DOTALL)
        return match.group(1) if match else ""
    
    async def _save_result(self, result: TaskResult):
        """保存任务结果"""
        # 保存JSON结果
        result_file = os.path.join(self.config.output_dir, f"{result.task_id}_result.json")
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(result), f, ensure_ascii=False, indent=2)
        
        # 保存最终代码
        if result.final_code:
            code_file = os.path.join(self.config.output_dir, f"{result.task_id}_final_code.py")
            with open(code_file, 'w', encoding='utf-8') as f:
                f.write(result.final_code)
    
    async def close(self):
        """关闭资源"""
        await self.model_client.close()


async def main():
    """主函数示例"""
    # 配置工作流
    config = WorkflowConfig(
        model_name="gpt-4o",
        max_iterations=2,
        enable_code_execution=True,
        enable_testing=True,
        temperature=0.1
    )
    
    # 创建工作流
    workflow = AdvancedProgrammingWorkflow(config)
    
    try:
        # 示例任务
        task = """
        设计并实现一个智能缓存系统，要求：
        
        1. 支持LRU（最近最少使用）淘汰策略
        2. 线程安全
        3. 支持设置最大容量
        4. 提供get、put、delete、clear操作
        5. 支持缓存统计（命中率、miss率等）
        6. 包含完整的错误处理和类型注解
        7. 提供使用示例和单元测试
        
        请确保代码具有良好的性能和可维护性。
        """
        
        # 运行任务
        result = await workflow.run_task(task)
        
        print(f"\n📊 任务结果摘要:")
        print(f"- 任务ID: {result.task_id}")
        print(f"- 成功状态: {result.success}")
        print(f"- 审查轮次: {len(result.review_history)}")
        print(f"- 优化轮次: {len(result.optimization_history)}")
        print(f"- 代码行数: {len(result.final_code.split('\n')) if result.final_code else 0}")
        
    finally:
        await workflow.close()


if __name__ == "__main__":
    asyncio.run(main())
