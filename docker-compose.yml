version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: mental-health-postgres
    environment:
      POSTGRES_DB: mental_health_analyzer
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - mental-health-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: mental-health-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mental-health-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Backend API
  backend:
    build:
      context: ./mental-health-backend
      dockerfile: Dockerfile
    container_name: mental-health-backend
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql+asyncpg://postgres:postgres@postgres:5432/mental_health_analyzer
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./mental-health-backend:/app
      - backend_uploads:/app/uploads
    networks:
      - mental-health-network
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Celery Worker
  celery-worker:
    build:
      context: ./mental-health-backend
      dockerfile: Dockerfile
    container_name: mental-health-celery-worker
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql+asyncpg://postgres:postgres@postgres:5432/mental_health_analyzer
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - postgres
      - redis
    volumes:
      - ./mental-health-backend:/app
    networks:
      - mental-health-network
    restart: unless-stopped
    command: celery -A app.tasks.celery_app worker --loglevel=info

  # Celery Beat (Scheduler)
  celery-beat:
    build:
      context: ./mental-health-backend
      dockerfile: Dockerfile
    container_name: mental-health-celery-beat
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql+asyncpg://postgres:postgres@postgres:5432/mental_health_analyzer
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - postgres
      - redis
    volumes:
      - ./mental-health-backend:/app
    networks:
      - mental-health-network
    restart: unless-stopped
    command: celery -A app.tasks.celery_app beat --loglevel=info

  # Flower (Celery Monitoring)
  flower:
    build:
      context: ./mental-health-backend
      dockerfile: Dockerfile
    container_name: mental-health-flower
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    ports:
      - "5555:5555"
    depends_on:
      - redis
    networks:
      - mental-health-network
    restart: unless-stopped
    command: celery -A app.tasks.celery_app flower --port=5555

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: mental-health-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - mental-health-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: mental-health-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - mental-health-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  backend_uploads:

networks:
  mental-health-network:
    driver: bridge
