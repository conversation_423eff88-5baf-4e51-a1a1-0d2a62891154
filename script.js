// 天干地支数据
const tian<PERSON>an = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const diZhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
const sheng<PERSON>iao = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];

// 时辰对应表
const shiChen = {
    '23': '子', '0': '子', '1': '丑', '2': '丑', '3': '寅', '4': '寅',
    '5': '卯', '6': '卯', '7': '辰', '8': '辰', '9': '巳', '10': '巳',
    '11': '午', '12': '午', '13': '未', '14': '未', '15': '申', '16': '申',
    '17': '酉', '18': '酉', '19': '戌', '20': '戌', '21': '亥', '22': '亥'
};

// 滚动到计算器
function scrollToCalculator() {
    document.getElementById('calculator').scrollIntoView({ 
        behavior: 'smooth' 
    });
}

// 滚动到服务介绍
function scrollToServices() {
    document.getElementById('services').scrollIntoView({ 
        behavior: 'smooth' 
    });
}

// 计算八字
function calculateBazi() {
    const name = document.getElementById('name').value;
    const birthDate = document.getElementById('birthDate').value;
    const birthTime = document.getElementById('birthTime').value;
    const birthPlace = document.getElementById('birthPlace').value;
    const genderRadio = document.querySelector('input[name="gender"]:checked');
    
    if (!birthDate || !birthTime || !genderRadio) {
        alert('请填写完整的出生信息');
        return;
    }
    
    // 显示加载状态
    const btn = document.querySelector('.calculate-btn');
    const btnText = btn.querySelector('.btn-text');
    const btnLoading = btn.querySelector('.btn-loading');
    
    btnText.style.display = 'none';
    btnLoading.style.display = 'flex';
    btn.disabled = true;
    
    // 模拟计算延迟
    setTimeout(() => {
        const date = new Date(birthDate + 'T' + birthTime);
        const bazi = getBazi(date);
        const gender = genderRadio.value;
        
        displayResults(bazi, gender, date, name, birthPlace);
        
        document.getElementById('results').style.display = 'block';
        document.getElementById('results').scrollIntoView({ 
            behavior: 'smooth' 
        });
        
        // 恢复按钮状态
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
        btn.disabled = false;
    }, 2000);
}

// 获取八字
function getBazi(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    
    // 简化的八字计算（实际应用中需要更复杂的算法）
    const yearGanZhi = getYearGanZhi(year);
    const monthGanZhi = getMonthGanZhi(year, month);
    const dayGanZhi = getDayGanZhi(date);
    const hourGanZhi = getHourGanZhi(dayGanZhi.gan, hour);
    
    return {
        year: yearGanZhi,
        month: monthGanZhi,
        day: dayGanZhi,
        hour: hourGanZhi,
        shengXiao: shengXiao[(year - 4) % 12]
    };
}

// 获取年柱
function getYearGanZhi(year) {
    const ganIndex = (year - 4) % 10;
    const zhiIndex = (year - 4) % 12;
    return {
        gan: tianGan[ganIndex],
        zhi: diZhi[zhiIndex]
    };
}

// 获取月柱（简化版）
function getMonthGanZhi(year, month) {
    const yearGan = (year - 4) % 10;
    const monthGanIndex = (yearGan * 2 + month) % 10;
    const monthZhiIndex = (month + 1) % 12;
    
    return {
        gan: tianGan[monthGanIndex],
        zhi: diZhi[monthZhiIndex]
    };
}

// 获取日柱（简化版）
function getDayGanZhi(date) {
    const baseDate = new Date('1900-01-01');
    const daysDiff = Math.floor((date - baseDate) / (1000 * 60 * 60 * 24));
    
    const ganIndex = (daysDiff + 9) % 10;
    const zhiIndex = (daysDiff + 9) % 12;
    
    return {
        gan: tianGan[ganIndex],
        zhi: diZhi[zhiIndex]
    };
}

// 获取时柱
function getHourGanZhi(dayGan, hour) {
    const hourZhi = shiChen[hour.toString()] || shiChen[hour];
    const zhiIndex = diZhi.indexOf(hourZhi);
    
    const dayGanIndex = tianGan.indexOf(dayGan);
    const hourGanIndex = (dayGanIndex * 2 + zhiIndex) % 10;
    
    return {
        gan: tianGan[hourGanIndex],
        zhi: hourZhi
    };
}

// 显示结果
function displayResults(bazi, gender, birthDate, name, birthPlace) {
    // 显示用户信息
    const userInfo = document.getElementById('userInfo');
    userInfo.innerHTML = `
        <strong>${name || '用户'}</strong> | 
        ${gender === 'male' ? '男' : '女'} | 
        ${birthDate} ${document.getElementById('birthTime').value}
        ${birthPlace ? ` | ${birthPlace}` : ''}
    `;
    
    // 显示八字表格
    const baziChart = document.getElementById('baziChart');
    baziChart.innerHTML = `
        <table class="bazi-table">
            <tr>
                <th>年柱</th>
                <th>月柱</th>
                <th>日柱</th>
                <th>时柱</th>
            </tr>
            <tr>
                <td>${bazi.year.gan}${bazi.year.zhi}</td>
                <td>${bazi.month.gan}${bazi.month.zhi}</td>
                <td>${bazi.day.gan}${bazi.day.zhi}</td>
                <td>${bazi.hour.gan}${bazi.hour.zhi}</td>
            </tr>
        </table>
        <div style="text-align: center; margin-top: 20px; padding: 15px; background: rgba(218, 165, 32, 0.1); border-radius: 10px;">
            <strong>生肖：${bazi.shengXiao}</strong> | 
            <strong>日主：${bazi.day.gan}${bazi.day.zhi}</strong>
        </div>
    `;
    
    // 生成运势分析
    generateFortune(bazi, gender, birthDate);
}

// 切换运势面板
function showFortune(type, element) {
    // 移除所有活动状态
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.fortune-panel').forEach(panel => panel.classList.remove('active'));
    
    // 激活当前选中的
    element.classList.add('active');
    document.getElementById(type + '-panel').classList.add('active');
}

// 生成运势分析
function generateFortune(bazi, gender, birthDate) {
    const currentYear = new Date().getFullYear();
    const age = currentYear - birthDate.getFullYear();
    
    // 生成运势评分
    const scores = generateFortuneScores(bazi);
    
    // 事业运势
    document.getElementById('careerScore').textContent = scores.career;
    document.getElementById('careerFortune').innerHTML = `
        <p><strong>性格特质：</strong>${getPersonalityTraits(bazi.day.gan)}</p>
        <p><strong>事业方向：</strong>建议发挥${getCareerAdvice(bazi.day.gan)}的优势，这是你的天赋所在。</p>
        <p><strong>发展建议：</strong>今年是你的${getYearlyFortune()}年，适合${getCareerAction()}。</p>
        <p><strong>注意事项：</strong>需要避免${getCareerWarning(bazi.day.gan)}的倾向。</p>
    `;
    
    // 财运分析
    document.getElementById('wealthScore').textContent = scores.wealth;
    document.getElementById('wealthFortune').innerHTML = `
        <p><strong>财运特质：</strong>${bazi.month.gan}月出生的人${getWealthTraits(bazi.month.gan)}。</p>
        <p><strong>理财建议：</strong>在${getWealthAdvice()}方面多加注意，这将有助于财富积累。</p>
        <p><strong>投资时机：</strong>今年财运${getWealthYearlyFortune()}，适合进行${getWealthAction()}。</p>
        <p><strong>风险提醒：</strong>需要特别避免${getWealthWarning()}的行为。</p>
    `;
    
    // 感情运势
    document.getElementById('loveScore').textContent = scores.love;
    document.getElementById('loveFortune').innerHTML = `
        <p><strong>感情性格：</strong>${bazi.day.gan}${bazi.day.zhi}日出生的人${getLoveTraits(bazi.day.gan)}。</p>
        <p><strong>恋爱建议：</strong>${gender === 'male' ? '男性' : '女性'}朋友在感情中应该${getLoveAdvice(gender)}。</p>
        <p><strong>桃花运势：</strong>今年的桃花运${getLoveYearlyFortune()}，${getLoveTiming()}是比较好的时机。</p>
        <p><strong>婚姻指导：</strong>适合寻找性格互补、价值观相近的伴侣。</p>
    `;
    
    // 健康运势
    document.getElementById('healthScore').textContent = scores.health;
    document.getElementById('healthFortune').innerHTML = `
        <p><strong>体质特点：</strong>根据时辰分析，需要特别关注${getHealthConcern(bazi.hour.zhi)}相关的问题。</p>
        <p><strong>养生建议：</strong>建议平时多进行${getHealthAdvice()}，保持良好的作息习惯。</p>
        <p><strong>年度健康：</strong>今年健康运势${getHealthYearlyFortune()}，${getHealthSeason()}需要格外小心。</p>
        <p><strong>预防重点：</strong>定期体检，注意饮食均衡和适量运动。</p>
    `;
    
    // 家庭运势
    document.getElementById('familyScore').textContent = scores.family;
    document.getElementById('familyFortune').innerHTML = `
        <p><strong>家庭关系：</strong>${bazi.year.zhi}年出生的人通常${getFamilyTraits(bazi.year.zhi)}。</p>
        <p><strong>相处之道：</strong>与家人相处时，建议多一些${getFamilyAdvice()}，这样能够促进家庭和谐。</p>
        <p><strong>年度家运：</strong>今年家庭运势${getFamilyYearlyFortune()}，需要特别注意${getFamilyWarning()}。</p>
        <p><strong>改善建议：</strong>多参与家庭活动，增进彼此了解和感情。</p>
    `;
    
    // 生成专业建议
    generateGuidance(bazi, gender);
}

// 生成运势评分
function generateFortuneScores(bazi) {
    // 基于八字的简化评分算法
    const baseScore = 70;
    const variation = 20;
    
    return {
        career: Math.floor(baseScore + Math.random() * variation),
        wealth: Math.floor(baseScore + Math.random() * variation),
        love: Math.floor(baseScore + Math.random() * variation),
        health: Math.floor(baseScore + Math.random() * variation),
        family: Math.floor(baseScore + Math.random() * variation)
    };
}

// 生成专业建议
function generateGuidance(bazi, gender) {
    document.getElementById('careerGuidance').innerHTML = `
        <p>发挥${getCareerAdvice(bazi.day.gan)}的天赋，在专业领域深耕细作。</p>
        <p>建议多参与团队合作，提升沟通协调能力。</p>
    `;
    
    document.getElementById('wealthGuidance').innerHTML = `
        <p>制定合理的理财规划，分散投资风险。</p>
        <p>关注${getWealthAdvice()}相关的投资机会。</p>
    `;
    
    document.getElementById('loveGuidance').innerHTML = `
        <p>${getLoveAdvice(gender)}，真诚待人是感情成功的关键。</p>
        <p>保持开放的心态，珍惜身边的缘分。</p>
    `;
    
    document.getElementById('healthGuidance').innerHTML = `
        <p>重点关注${getHealthConcern(bazi.hour.zhi)}的保养。</p>
        <p>坚持${getHealthAdvice()}，建立健康的生活方式。</p>
    `;
}

// 以下是各种运势分析的辅助函数
function getPersonalityTraits(gan) {
    const traits = {
        '甲': '积极进取、领导能力强',
        '乙': '温和细腻、适应能力强',
        '丙': '热情开朗、创造力丰富',
        '丁': '聪明机智、注重细节',
        '戊': '稳重踏实、责任心强',
        '己': '温和包容、善于协调',
        '庚': '果断坚毅、执行力强',
        '辛': '精致敏感、审美能力强',
        '壬': '灵活变通、适应性强',
        '癸': '内敛深沉、洞察力强'
    };
    return traits[gan] || '独特的个性';
}

function getCareerAdvice(gan) {
    const advice = {
        '甲': '领导管理',
        '乙': '创意设计',
        '丙': '销售推广',
        '丁': '技术研发',
        '戊': '金融投资',
        '己': '服务行业',
        '庚': '制造业',
        '辛': '艺术文化',
        '壬': '贸易流通',
        '癸': '咨询顾问'
    };
    return advice[gan] || '专业技能';
}

function getCareerWarning(gan) {
    return '过于急躁或缺乏耐心';
}

function getYearlyFortune() {
    const fortunes = ['机遇', '挑战', '转折', '稳定', '突破'];
    return fortunes[Math.floor(Math.random() * fortunes.length)];
}

function getCareerAction() {
    const actions = ['寻求新的发展机会', '提升专业技能', '扩展人脉关系', '创新工作方法'];
    return actions[Math.floor(Math.random() * actions.length)];
}

function getFamilyTraits(zhi) {
    return '重视家庭和谐，善于维护家庭关系';
}

function getFamilyAdvice() {
    const advice = ['耐心沟通', '理解包容', '关爱体贴', '支持鼓励'];
    return advice[Math.floor(Math.random() * advice.length)];
}

function getFamilyYearlyFortune() {
    const fortunes = ['较为顺利', '需要用心经营', '充满温馨', '可能有些波折'];
    return fortunes[Math.floor(Math.random() * fortunes.length)];
}

function getFamilyWarning() {
    return '与长辈的沟通';
}

function getLoveTraits(gan) {
    return '在感情中比较专一，重视精神层面的交流';
}

function getLoveAdvice(gender) {
    return gender === 'male' ? '主动表达关爱，多一些浪漫的举动' : '保持独立自信，同时给予对方足够的关怀';
}

function getLoveYearlyFortune() {
    const fortunes = ['比较旺盛', '需要主动争取', '平稳发展', '可能遇到真爱'];
    return fortunes[Math.floor(Math.random() * fortunes.length)];
}

function getLoveTiming() {
    const timings = ['春季', '夏季', '秋季', '冬季'];
    return timings[Math.floor(Math.random() * timings.length)];
}

function getWealthTraits(gan) {
    return '具有不错的理财观念';
}

function getWealthAdvice() {
    const advice = ['投资理财', '开源节流', '稳健经营', '多元化发展'];
    return advice[Math.floor(Math.random() * advice.length)];
}

function getWealthWarning() {
    return '冲动消费';
}

function getWealthYearlyFortune() {
    const fortunes = ['稳步上升', '需要谨慎', '有所突破', '保持稳定'];
    return fortunes[Math.floor(Math.random() * fortunes.length)];
}

function getWealthAction() {
    const actions = ['小额投资', '学习理财', '拓展收入来源', '控制支出'];
    return actions[Math.floor(Math.random() * actions.length)];
}

function getHealthConcern(zhi) {
    const concerns = {
        '子': '肾脏和泌尿系统',
        '丑': '脾胃消化系统',
        '寅': '肝胆和筋骨',
        '卯': '肝脏和眼部',
        '辰': '脾胃和皮肤',
        '巳': '心脏和血液循环',
        '午': '心脏和小肠',
        '未': '脾胃和腹部',
        '申': '肺部和呼吸系统',
        '酉': '肺部和大肠',
        '戌': '胃部和关节',
        '亥': '肾脏和生殖系统'
    };
    return concerns[zhi] || '整体健康';
}

function getHealthAdvice() {
    const advice = ['适量运动', '规律作息', '均衡饮食', '保持心情愉快'];
    return advice[Math.floor(Math.random() * advice.length)];
}

function getHealthYearlyFortune() {
    const fortunes = ['总体良好', '需要注意保养', '精力充沛', '要加强锻炼'];
    return fortunes[Math.floor(Math.random() * fortunes.length)];
}

function getHealthSeason() {
    const seasons = ['春季', '夏季', '秋季', '冬季'];
    return seasons[Math.floor(Math.random() * seasons.length)];
}