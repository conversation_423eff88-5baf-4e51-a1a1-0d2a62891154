# 心理健康内容分析系统

一个专注于心理健康和情绪挑战领域的智能内容采集与分析平台，通过自动化采集社交媒体上的相关故事内容，分析其模式和传播规律，为内容创作者和心理健康从业者提供数据驱动的洞察。

## 🎯 产品特色

- **🧠 AI驱动分析**: 支持Google Gemini Pro和OpenAI GPT-4进行故事分类、情感分析和模式识别
- **📊 多平台采集**: 支持Reddit、YouTube、TikTok、Podcast等主流平台
- **🔍 智能过滤**: 基于真实度、互动率和付费潜力的多维度内容筛选
- **📈 趋势洞察**: 实时监控心理健康内容趋势和用户互动模式
- **⚠️ 风险识别**: 自动检测高风险内容，提供及时预警

## 技术架构

### 前端
- React 18 + TypeScript
- Ant Design UI组件库
- ECharts数据可视化
- Vite构建工具

### 后端
- Node.js + Express
- TypeScript
- MySQL数据库
- Redis缓存

## 快速开始

### 环境要求

- Node.js >= 18.0.0
- MySQL >= 8.0
- Redis >= 6.0

### 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 环境配置

1. 复制环境配置文件：
```bash
# 后端环境配置
cd backend
cp .env.example .env
```

2. 修改 `.env` 文件中的数据库和Redis连接信息

3. 创建数据库：
```sql
CREATE DATABASE social_content_collector;
```

### 启动服务

```bash
# 启动后端服务
cd backend
npm run dev

# 启动前端服务
cd frontend
npm run dev
```

访问 http://localhost:3001 查看应用

## 项目结构

```
├── backend/                 # 后端服务
│   ├── src/
│   │   ├── config/         # 配置文件
│   │   ├── controllers/    # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   ├── services/       # 业务逻辑
│   │   └── types/          # 类型定义
│   └── package.json
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── services/       # API服务
│   │   └── types/          # 类型定义
│   └── package.json
└── README.md
```

## API文档

### 任务管理
- `POST /api/tasks` - 创建采集任务
- `GET /api/tasks` - 获取任务列表
- `GET /api/tasks/:id` - 获取任务详情
- `PUT /api/tasks/:id/start` - 启动任务
- `PUT /api/tasks/:id/pause` - 暂停任务

### 内容管理
- `GET /api/content` - 获取内容列表
- `GET /api/content/hot` - 获取热点内容
- `GET /api/content/search` - 搜索内容

### 分析报告
- `GET /api/analysis/report/:taskId` - 获取任务分析报告
- `GET /api/analysis/export/:taskId` - 导出分析数据

## 开发指南

### 代码规范

项目使用TypeScript + ESLint + Prettier进行代码规范管理。

### 测试

```bash
# 运行后端测试
cd backend
npm test

# 运行前端测试
cd frontend
npm test
```

### 构建部署

```bash
# 构建后端
cd backend
npm run build

# 构建前端
cd frontend
npm run build
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 🤖 AI模型配置

### Gemini Pro (推荐)
本系统支持Google Gemini Pro，提供更强的分析能力：

```env
AI_PROVIDER=gemini
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL=gemini-1.5-pro  # 或 gemini-1.5-flash
```

**Gemini Pro优势**:
- ✅ 更高质量的心理健康内容分析
- ✅ 更大的上下文窗口 (支持更长文本)
- ✅ 更高的API速率限制 (60 req/min vs 15 req/min)
- ✅ 相比OpenAI更具成本效益

**获取API密钥**: [Google AI Studio](https://makersuite.google.com/app/apikey)

详细配置指南请参考: [GEMINI_PRO_SETUP.md](GEMINI_PRO_SETUP.md)

### 测试AI配置
```bash
# 测试Gemini Pro
python test_gemini_pro.py

# 测试基础功能
python test_reddit_scraper.py
```

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请提交 Issue 或联系开发团队。