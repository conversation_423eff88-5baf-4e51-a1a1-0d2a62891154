#!/usr/bin/env python3
"""
AutoGen 编程工作流演示脚本
展示如何使用三个 Agent 协作完成编程任务
"""

import asyncio
import os
import sys
from typing import Optional

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from autogen_programming_workflow import ProgrammingWorkflow
from advanced_autogen_workflow import AdvancedProgrammingWorkflow, WorkflowConfig


async def demo_basic_workflow():
    """演示基础工作流"""
    print("🚀 演示基础 AutoGen 编程工作流")
    print("=" * 60)
    
    # 创建基础工作流
    workflow = ProgrammingWorkflow(model_name="gpt-4o-mini")
    
    try:
        # 定义一个简单的编程任务
        task = """
        请编写一个Python函数来实现快速排序算法，要求：
        
        1. 函数名为 quick_sort
        2. 接受一个整数列表作为参数
        3. 返回排序后的列表
        4. 包含完整的类型注解
        5. 添加详细的文档字符串
        6. 处理边界情况（空列表、单元素列表）
        7. 提供使用示例
        
        请确保代码具有良好的可读性和性能。
        """
        
        print("📝 任务描述:")
        print(task)
        print("\n🔄 开始执行工作流...")
        
        # 运行工作流
        await workflow.run_programming_task(task)
        
        print("\n✅ 基础工作流演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        print("💡 提示: 请确保已设置 OPENAI_API_KEY 环境变量")
    
    finally:
        await workflow.close()


async def demo_advanced_workflow():
    """演示高级工作流"""
    print("\n🚀 演示高级 AutoGen 编程工作流")
    print("=" * 60)
    
    # 配置高级工作流
    config = WorkflowConfig(
        model_name="gpt-4o-mini",  # 使用较便宜的模型进行演示
        max_iterations=2,
        enable_code_execution=False,  # 演示时关闭代码执行
        enable_testing=True,
        output_dir="demo_output",
        temperature=0.1
    )
    
    # 创建高级工作流
    workflow = AdvancedProgrammingWorkflow(config)
    
    try:
        # 定义一个更复杂的编程任务
        task = """
        设计并实现一个简单的任务队列系统，要求：
        
        功能需求：
        1. 支持添加任务到队列
        2. 支持从队列中获取任务
        3. 支持任务优先级（高、中、低）
        4. 支持查看队列状态（任务数量、队列是否为空）
        5. 线程安全设计
        
        技术要求：
        1. 使用面向对象设计
        2. 包含完整的类型注解
        3. 添加详细的文档字符串
        4. 实现适当的异常处理
        5. 提供使用示例和简单测试
        
        性能要求：
        1. 高效的插入和删除操作
        2. 内存使用优化
        3. 支持大量任务处理
        
        请提供完整的实现和使用说明。
        """
        
        print("📝 任务描述:")
        print(task)
        print(f"\n⚙️ 工作流配置:")
        print(f"   - 模型: {config.model_name}")
        print(f"   - 最大迭代: {config.max_iterations}")
        print(f"   - 代码执行: {config.enable_code_execution}")
        print(f"   - 输出目录: {config.output_dir}")
        
        print("\n🔄 开始执行高级工作流...")
        
        # 运行工作流
        result = await workflow.run_task(task, task_id="demo_task_queue")
        
        # 显示结果摘要
        print(f"\n📊 执行结果摘要:")
        print(f"   ✅ 任务成功: {result.success}")
        print(f"   📝 代码行数: {len(result.final_code.split('\n')) if result.final_code else 0}")
        print(f"   🔍 审查轮次: {len(result.review_history)}")
        print(f"   ⚡ 优化轮次: {len(result.optimization_history)}")
        print(f"   📁 输出目录: {config.output_dir}")
        
        # 如果有最终代码，显示前几行
        if result.final_code:
            print(f"\n📄 最终代码预览（前10行）:")
            lines = result.final_code.split('\n')[:10]
            for i, line in enumerate(lines, 1):
                print(f"   {i:2d}: {line}")
            if len(result.final_code.split('\n')) > 10:
                print(f"   ... (还有 {len(result.final_code.split('\n')) - 10} 行)")
        
        print("\n✅ 高级工作流演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        print("💡 提示: 请确保已设置 OPENAI_API_KEY 环境变量")
    
    finally:
        await workflow.close()


async def demo_interactive_mode():
    """演示交互式模式"""
    print("\n🤖 交互式演示模式")
    print("=" * 60)
    
    # 简单的交互式演示
    config = WorkflowConfig(
        model_name="gpt-4o-mini",
        max_iterations=1,
        enable_code_execution=False,
        output_dir="interactive_demo_output"
    )
    
    workflow = AdvancedProgrammingWorkflow(config)
    
    try:
        print("请输入一个简单的编程任务（按回车确认，输入 'skip' 跳过）:")
        user_input = input("> ").strip()
        
        if user_input and user_input.lower() != 'skip':
            print(f"\n🔄 处理任务: {user_input}")
            result = await workflow.run_task(user_input, task_id="interactive_demo")
            
            print(f"\n✅ 任务完成！结果已保存到: {config.output_dir}")
            
            if result.final_code:
                print("\n📄 生成的代码:")
                print("```python")
                print(result.final_code[:500] + "..." if len(result.final_code) > 500 else result.final_code)
                print("```")
        else:
            print("⏭️ 跳过交互式演示")
    
    except Exception as e:
        print(f"❌ 交互式演示出现错误: {e}")
    
    finally:
        await workflow.close()


def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查 OpenAI API Key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("⚠️  警告: 未设置 OPENAI_API_KEY 环境变量")
        print("   请设置您的 OpenAI API 密钥:")
        print("   export OPENAI_API_KEY='your-api-key-here'")
        return False
    else:
        print(f"✅ OpenAI API Key 已设置 (长度: {len(api_key)})")
    
    # 检查必要的包
    try:
        import autogen_agentchat
        import autogen_ext
        print("✅ AutoGen 包已安装")
    except ImportError as e:
        print(f"❌ AutoGen 包未正确安装: {e}")
        print("   请运行: pip install -r requirements.txt")
        return False
    
    return True


async def main():
    """主演示函数"""
    print("🎭 AutoGen 编程工作流演示")
    print("=" * 80)
    print("本演示将展示如何使用 AutoGen 框架创建智能编程工作流")
    print("包含三个协作的 AI Agent：代码编写者、审查者和优化者")
    print("=" * 80)
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请先配置环境后再运行演示")
        return
    
    print("\n请选择演示模式:")
    print("1. 基础工作流演示（简单快速）")
    print("2. 高级工作流演示（功能完整）")
    print("3. 交互式演示（自定义任务）")
    print("4. 运行所有演示")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            await demo_basic_workflow()
        elif choice == "2":
            await demo_advanced_workflow()
        elif choice == "3":
            await demo_interactive_mode()
        elif choice == "4":
            await demo_basic_workflow()
            await demo_advanced_workflow()
            await demo_interactive_mode()
        else:
            print("❌ 无效选择，退出演示")
            return
        
        print("\n🎉 演示完成！")
        print("\n📚 更多信息:")
        print("   - 查看 AUTOGEN_WORKFLOW_README.md 了解详细使用说明")
        print("   - 运行 python workflow_examples.py 体验更多示例")
        print("   - 运行 python test_workflow.py 执行测试")
        
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出现意外错误: {e}")


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
