"""
AutoGen 编程工作流使用示例
展示不同类型的编程任务和工作流配置
"""

import asyncio
import os
from typing import List, Dict, Any

from advanced_autogen_workflow import AdvancedProgrammingWorkflow, WorkflowConfig


class WorkflowExamples:
    """工作流示例集合"""
    
    @staticmethod
    def get_basic_config() -> WorkflowConfig:
        """基础配置"""
        return WorkflowConfig(
            model_name="gpt-4o-mini",
            max_iterations=2,
            enable_code_execution=False,
            enable_testing=True,
            temperature=0.1
        )
    
    @staticmethod
    def get_advanced_config() -> WorkflowConfig:
        """高级配置"""
        return WorkflowConfig(
            model_name="gpt-4o",
            max_iterations=3,
            enable_code_execution=True,
            enable_testing=True,
            temperature=0.0
        )
    
    @staticmethod
    def get_example_tasks() -> List[Dict[str, Any]]:
        """获取示例任务列表"""
        return [
            {
                "name": "数据结构实现",
                "description": """
                实现一个高效的二叉搜索树（BST），要求：
                1. 支持插入、删除、查找操作
                2. 实现中序遍历、前序遍历、后序遍历
                3. 支持查找最小值、最大值
                4. 计算树的高度和节点数量
                5. 包含完整的类型注解和文档字符串
                6. 提供详细的使用示例
                """,
                "config": "basic"
            },
            {
                "name": "算法优化",
                "description": """
                实现一个高效的字符串匹配算法，要求：
                1. 实现KMP算法进行模式匹配
                2. 支持多模式匹配（Aho-Corasick算法）
                3. 提供性能基准测试
                4. 包含边界情况处理
                5. 支持大文件处理
                6. 提供详细的时间复杂度分析
                """,
                "config": "advanced"
            },
            {
                "name": "设计模式应用",
                "description": """
                设计一个可扩展的日志系统，要求：
                1. 使用工厂模式创建不同类型的日志器
                2. 使用装饰器模式添加日志格式化功能
                3. 使用观察者模式实现日志事件通知
                4. 支持多种输出目标（文件、控制台、网络）
                5. 支持日志级别过滤
                6. 线程安全设计
                7. 配置文件支持
                """,
                "config": "advanced"
            },
            {
                "name": "Web API 开发",
                "description": """
                使用FastAPI开发一个RESTful API，要求：
                1. 实现用户认证和授权（JWT）
                2. 数据库集成（SQLAlchemy + PostgreSQL）
                3. 请求验证和错误处理
                4. API文档自动生成
                5. 单元测试和集成测试
                6. Docker容器化部署
                7. 性能监控和日志记录
                """,
                "config": "advanced"
            },
            {
                "name": "机器学习工具",
                "description": """
                开发一个机器学习模型训练工具，要求：
                1. 支持多种算法（线性回归、决策树、随机森林）
                2. 数据预处理管道
                3. 特征工程和选择
                4. 模型评估和交叉验证
                5. 超参数调优
                6. 模型持久化和加载
                7. 可视化结果展示
                """,
                "config": "advanced"
            },
            {
                "name": "并发编程",
                "description": """
                实现一个高性能的任务调度器，要求：
                1. 支持异步任务执行
                2. 任务优先级队列
                3. 工作线程池管理
                4. 任务依赖关系处理
                5. 错误重试机制
                6. 任务状态监控
                7. 性能指标收集
                8. 优雅关闭机制
                """,
                "config": "advanced"
            }
        ]


async def run_single_example(task_info: Dict[str, Any]):
    """运行单个示例任务"""
    print(f"\n🎯 开始执行任务: {task_info['name']}")
    print("=" * 80)
    
    # 选择配置
    if task_info['config'] == 'basic':
        config = WorkflowExamples.get_basic_config()
    else:
        config = WorkflowExamples.get_advanced_config()
    
    # 创建工作流
    workflow = AdvancedProgrammingWorkflow(config)
    
    try:
        # 运行任务
        result = await workflow.run_task(
            task_description=task_info['description'],
            task_id=f"{task_info['name'].lower().replace(' ', '_')}"
        )
        
        # 显示结果摘要
        print(f"\n📊 任务 '{task_info['name']}' 完成:")
        print(f"   ✅ 成功: {result.success}")
        print(f"   📝 代码行数: {len(result.final_code.split('\n')) if result.final_code else 0}")
        print(f"   🔍 审查轮次: {len(result.review_history)}")
        print(f"   ⚡ 优化轮次: {len(result.optimization_history)}")
        
        return result
        
    finally:
        await workflow.close()


async def run_batch_examples(task_names: List[str] = None):
    """批量运行示例任务"""
    tasks = WorkflowExamples.get_example_tasks()
    
    if task_names:
        tasks = [task for task in tasks if task['name'] in task_names]
    
    results = []
    
    for task_info in tasks:
        try:
            result = await run_single_example(task_info)
            results.append(result)
        except Exception as e:
            print(f"❌ 任务 '{task_info['name']}' 执行失败: {e}")
    
    # 显示批量结果摘要
    print(f"\n📈 批量执行摘要:")
    print(f"   总任务数: {len(tasks)}")
    print(f"   成功任务数: {sum(1 for r in results if r.success)}")
    print(f"   失败任务数: {len(tasks) - len(results)}")
    
    return results


async def interactive_mode():
    """交互式模式"""
    print("🤖 AutoGen 编程工作流 - 交互式模式")
    print("=" * 50)
    
    # 选择配置
    print("请选择配置:")
    print("1. 基础配置 (gpt-4o-mini, 无代码执行)")
    print("2. 高级配置 (gpt-4o, 包含代码执行)")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        config = WorkflowExamples.get_basic_config()
        print("✅ 已选择基础配置")
    else:
        config = WorkflowExamples.get_advanced_config()
        print("✅ 已选择高级配置")
    
    # 创建工作流
    workflow = AdvancedProgrammingWorkflow(config)
    
    try:
        while True:
            print("\n" + "=" * 50)
            print("请输入编程任务描述 (输入 'quit' 退出):")
            
            task_description = input("> ").strip()
            
            if task_description.lower() in ['quit', 'exit', 'q']:
                break
            
            if not task_description:
                print("❌ 任务描述不能为空")
                continue
            
            try:
                result = await workflow.run_task(task_description)
                
                print(f"\n✅ 任务完成!")
                print(f"📁 结果已保存到: {config.output_dir}")
                
                # 询问是否查看代码
                if result.final_code:
                    show_code = input("\n是否显示最终代码? (y/n): ").strip().lower()
                    if show_code == 'y':
                        print("\n" + "=" * 50)
                        print("📄 最终代码:")
                        print(result.final_code)
                        print("=" * 50)
                
            except Exception as e:
                print(f"❌ 任务执行失败: {e}")
    
    finally:
        await workflow.close()
        print("\n👋 感谢使用 AutoGen 编程工作流!")


async def main():
    """主函数"""
    print("🚀 AutoGen 编程工作流示例")
    print("=" * 50)
    print("请选择运行模式:")
    print("1. 运行单个示例任务")
    print("2. 批量运行所有示例")
    print("3. 交互式模式")
    print("4. 查看示例任务列表")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == "1":
        # 显示任务列表
        tasks = WorkflowExamples.get_example_tasks()
        print("\n可用的示例任务:")
        for i, task in enumerate(tasks, 1):
            print(f"{i}. {task['name']}")
        
        task_choice = input(f"\n请选择任务 (1-{len(tasks)}): ").strip()
        try:
            task_index = int(task_choice) - 1
            if 0 <= task_index < len(tasks):
                await run_single_example(tasks[task_index])
            else:
                print("❌ 无效的任务选择")
        except ValueError:
            print("❌ 请输入有效的数字")
    
    elif choice == "2":
        await run_batch_examples()
    
    elif choice == "3":
        await interactive_mode()
    
    elif choice == "4":
        tasks = WorkflowExamples.get_example_tasks()
        print("\n📋 示例任务列表:")
        for i, task in enumerate(tasks, 1):
            print(f"\n{i}. {task['name']}")
            print(f"   配置: {task['config']}")
            print(f"   描述: {task['description'][:100]}...")
    
    else:
        print("❌ 无效的选择")


if __name__ == "__main__":
    asyncio.run(main())
