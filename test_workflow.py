"""
AutoGen 编程工作流测试文件
用于验证工作流的基本功能
"""

import asyncio
import os
import tempfile
import pytest
from unittest.mock import Mock, AsyncMock

from autogen_programming_workflow import ProgrammingWorkflow
from advanced_autogen_workflow import AdvancedProgrammingWorkflow, WorkflowConfig


class TestBasicWorkflow:
    """基础工作流测试"""
    
    @pytest.mark.asyncio
    async def test_workflow_initialization(self):
        """测试工作流初始化"""
        workflow = ProgrammingWorkflow(model_name="gpt-4o-mini")
        
        # 验证 agents 已创建
        assert workflow.code_writer is not None
        assert workflow.code_reviewer is not None
        assert workflow.code_optimizer is not None
        
        # 验证工作流图已构建
        assert workflow.workflow is not None
        
        await workflow.close()
    
    @pytest.mark.asyncio
    async def test_simple_task_structure(self):
        """测试简单任务的结构"""
        # 使用模拟的模型客户端
        mock_client = Mock()
        mock_client.close = AsyncMock()
        
        workflow = ProgrammingWorkflow(model_name="gpt-4o-mini")
        workflow.model_client = mock_client
        
        # 验证基本结构
        assert hasattr(workflow, 'code_writer')
        assert hasattr(workflow, 'code_reviewer')
        assert hasattr(workflow, 'code_optimizer')
        
        await workflow.close()


class TestAdvancedWorkflow:
    """高级工作流测试"""
    
    def test_config_creation(self):
        """测试配置创建"""
        config = WorkflowConfig(
            model_name="gpt-4o-mini",
            max_iterations=2,
            enable_code_execution=False,
            temperature=0.0
        )
        
        assert config.model_name == "gpt-4o-mini"
        assert config.max_iterations == 2
        assert config.enable_code_execution is False
        assert config.temperature == 0.0
    
    @pytest.mark.asyncio
    async def test_workflow_with_config(self):
        """测试使用配置的工作流"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = WorkflowConfig(
                model_name="gpt-4o-mini",
                max_iterations=1,
                enable_code_execution=False,
                output_dir=temp_dir,
                temperature=0.0
            )
            
            workflow = AdvancedProgrammingWorkflow(config)
            
            # 验证配置应用
            assert workflow.config.model_name == "gpt-4o-mini"
            assert workflow.config.max_iterations == 1
            assert workflow.config.output_dir == temp_dir
            
            # 验证输出目录创建
            assert os.path.exists(temp_dir)
            
            await workflow.close()
    
    def test_code_extraction(self):
        """测试代码提取功能"""
        config = WorkflowConfig(enable_code_execution=False)
        workflow = AdvancedProgrammingWorkflow(config)
        
        # 测试代码提取
        content_with_code = """
        这是一些说明文字
        
        ```python
        def hello_world():
            print("Hello, World!")
            return "success"
        ```
        
        更多说明文字
        """
        
        extracted_code = workflow._extract_code(content_with_code)
        expected_code = 'def hello_world():\n    print("Hello, World!")\n    return "success"'
        
        assert extracted_code == expected_code
        
        # 测试无代码情况
        content_without_code = "这里没有代码块"
        extracted_code = workflow._extract_code(content_without_code)
        assert extracted_code == ""


class TestWorkflowIntegration:
    """工作流集成测试"""
    
    @pytest.mark.asyncio
    async def test_mock_task_execution(self):
        """测试模拟任务执行"""
        # 创建模拟的工作流
        config = WorkflowConfig(
            model_name="gpt-4o-mini",
            max_iterations=1,
            enable_code_execution=False,
            temperature=0.0
        )
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config.output_dir = temp_dir
            workflow = AdvancedProgrammingWorkflow(config)
            
            # 模拟任务结果处理
            mock_messages = [
                Mock(content="代码编写完成", source="CodeWriter"),
                Mock(content="代码审查通过 APPROVED", source="CodeReviewer"),
                Mock(content="```python\ndef test(): pass\n```\nOPTIMIZATION_COMPLETE", source="CodeOptimizer")
            ]
            
            mock_result = Mock()
            mock_result.messages = mock_messages
            
            # 测试结果处理
            task_result = workflow._process_result("test_task", "测试任务", mock_result)
            
            assert task_result.task_id == "test_task"
            assert task_result.description == "测试任务"
            assert len(task_result.review_history) == 1
            assert len(task_result.optimization_history) == 1
            assert "def test(): pass" in task_result.final_code
            
            await workflow.close()


def test_example_tasks_structure():
    """测试示例任务结构"""
    from workflow_examples import WorkflowExamples
    
    tasks = WorkflowExamples.get_example_tasks()
    
    # 验证任务结构
    assert len(tasks) > 0
    
    for task in tasks:
        assert 'name' in task
        assert 'description' in task
        assert 'config' in task
        assert task['config'] in ['basic', 'advanced']
        assert isinstance(task['description'], str)
        assert len(task['description']) > 0


def test_config_variations():
    """测试不同配置变体"""
    from workflow_examples import WorkflowExamples
    
    # 测试基础配置
    basic_config = WorkflowExamples.get_basic_config()
    assert basic_config.model_name == "gpt-4o-mini"
    assert basic_config.enable_code_execution is False
    
    # 测试高级配置
    advanced_config = WorkflowExamples.get_advanced_config()
    assert advanced_config.model_name == "gpt-4o"
    assert advanced_config.enable_code_execution is True
    assert advanced_config.temperature == 0.0


class TestErrorHandling:
    """错误处理测试"""
    
    @pytest.mark.asyncio
    async def test_invalid_config(self):
        """测试无效配置处理"""
        # 测试负数迭代次数
        config = WorkflowConfig(max_iterations=-1)
        
        # 应该能创建工作流，但可能在运行时出错
        workflow = AdvancedProgrammingWorkflow(config)
        assert workflow.config.max_iterations == -1
        
        await workflow.close()
    
    def test_empty_code_extraction(self):
        """测试空代码提取"""
        config = WorkflowConfig(enable_code_execution=False)
        workflow = AdvancedProgrammingWorkflow(config)
        
        # 测试空字符串
        assert workflow._extract_code("") == ""
        
        # 测试只有文本
        assert workflow._extract_code("只有文本内容") == ""
        
        # 测试不完整的代码块
        assert workflow._extract_code("```python\n没有结束标记") == ""


if __name__ == "__main__":
    # 运行基本测试
    print("🧪 运行 AutoGen 工作流测试")
    
    # 运行同步测试
    test_example_tasks_structure()
    test_config_variations()
    print("✅ 同步测试通过")
    
    # 运行异步测试示例
    async def run_async_tests():
        test_workflow = TestBasicWorkflow()
        await test_workflow.test_workflow_initialization()
        print("✅ 基础工作流测试通过")
        
        test_advanced = TestAdvancedWorkflow()
        await test_advanced.test_workflow_with_config()
        print("✅ 高级工作流测试通过")
        
        test_integration = TestWorkflowIntegration()
        await test_integration.test_mock_task_execution()
        print("✅ 集成测试通过")
    
    # 运行异步测试
    asyncio.run(run_async_tests())
    
    print("🎉 所有测试完成！")
