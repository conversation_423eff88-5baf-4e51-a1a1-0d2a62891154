# Design Document

## Overview

社交内容采集系统是一个基于Web的多平台内容采集和分析工具。系统采用前后端分离架构，前端提供用户友好的任务配置和数据展示界面，后端负责多平台数据采集、处理和存储。系统支持小红书、抖音等主流平台的内容采集，并提供智能分析和报告生成功能。

## Architecture

### 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A[Web界面] --> B[任务配置]
        A --> C[数据展示]
        A --> D[报告分析]
    end
    
    subgraph "API网关层"
        E[API Gateway]
    end
    
    subgraph "业务服务层"
        F[任务管理服务]
        G[采集服务]
        H[数据处理服务]
        I[分析服务]
    end
    
    subgraph "数据采集层"
        J[小红书采集器]
        K[抖音采集器]
        L[微信采集器]
        M[通用爬虫引擎]
    end
    
    subgraph "数据存储层"
        N[MySQL数据库]
        O[Redis缓存]
        P[文件存储]
    end
    
    A --> E
    E --> F
    E --> G
    E --> H
    E --> I
    
    F --> N
    G --> J
    G --> K
    G --> L
    J --> M
    K --> M
    L --> M
    
    H --> N
    H --> O
    I --> N
    
    M --> P
```

### 技术栈选择

**前端技术栈：**
- React.js + TypeScript - 主框架
- Ant Design - UI组件库
- ECharts - 数据可视化
- Axios - HTTP客户端

**后端技术栈：**
- Node.js + Express - Web框架
- TypeScript - 开发语言
- MySQL - 主数据库
- Redis - 缓存和队列
- Puppeteer - 浏览器自动化
- Cheerio - HTML解析

## Components and Interfaces

### 前端组件设计

#### 1. 任务配置组件 (TaskConfigComponent)
```typescript
interface TaskConfig {
  name: string;
  platforms: Platform[];
  keywords: string[];
  filters: FilterConfig;
  schedule?: ScheduleConfig;
}

interface FilterConfig {
  minFollowers?: number;
  minLikes?: number;
  minComments?: number;
  contentType?: ContentType[];
  timeRange?: DateRange;
}
```

#### 2. 任务监控组件 (TaskMonitorComponent)
```typescript
interface TaskStatus {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  collectedCount: number;
  estimatedTime?: number;
  error?: string;
}
```

#### 3. 数据展示组件 (DataDisplayComponent)
```typescript
interface ContentItem {
  id: string;
  platform: Platform;
  author: AuthorInfo;
  content: ContentInfo;
  metrics: MetricsInfo;
  originalUrl: string;
  collectedAt: Date;
}
```

### 后端服务接口

#### 1. 任务管理服务 (TaskService)
```typescript
class TaskService {
  async createTask(config: TaskConfig): Promise<Task>;
  async startTask(taskId: string): Promise<void>;
  async pauseTask(taskId: string): Promise<void>;
  async getTaskStatus(taskId: string): Promise<TaskStatus>;
  async getTaskHistory(userId: string): Promise<Task[]>;
}
```

#### 2. 采集服务 (CollectorService)
```typescript
class CollectorService {
  async collectFromPlatform(platform: Platform, config: CollectConfig): Promise<ContentItem[]>;
  async validatePlatformAccess(platform: Platform): Promise<boolean>;
  async getHotContent(platform: Platform): Promise<ContentItem[]>;
}
```

#### 3. 数据处理服务 (DataProcessService)
```typescript
class DataProcessService {
  async processContent(rawContent: RawContent): Promise<ContentItem>;
  async deduplicateContent(contents: ContentItem[]): Promise<ContentItem[]>;
  async categorizeContent(content: ContentItem): Promise<ContentCategory>;
  async extractKeywords(content: string): Promise<string[]>;
}
```

## Data Models

### 核心数据模型

#### 1. 任务模型 (Task)
```sql
CREATE TABLE tasks (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  name VARCHAR(255) NOT NULL,
  config JSON NOT NULL,
  status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
  progress INT DEFAULT 0,
  collected_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_status (status)
);
```

#### 2. 内容模型 (Content)
```sql
CREATE TABLE contents (
  id VARCHAR(36) PRIMARY KEY,
  task_id VARCHAR(36) NOT NULL,
  platform ENUM('xiaohongshu', 'douyin', 'wechat_article', 'wechat_video') NOT NULL,
  author_id VARCHAR(255) NOT NULL,
  author_name VARCHAR(255) NOT NULL,
  author_followers INT,
  content_id VARCHAR(255) NOT NULL,
  content_type ENUM('text', 'image', 'video', 'mixed') NOT NULL,
  title TEXT,
  description TEXT,
  content_text TEXT,
  media_urls JSON,
  tags JSON,
  metrics JSON NOT NULL,
  original_url VARCHAR(500),
  published_at TIMESTAMP,
  collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  category ENUM('psychology', 'growth', 'relationship', 'other'),
  FOREIGN KEY (task_id) REFERENCES tasks(id),
  INDEX idx_task_id (task_id),
  INDEX idx_platform (platform),
  INDEX idx_category (category),
  INDEX idx_published_at (published_at)
);
```

#### 3. 指标模型 (Metrics)
```sql
CREATE TABLE content_metrics (
  content_id VARCHAR(36) PRIMARY KEY,
  likes_count INT DEFAULT 0,
  comments_count INT DEFAULT 0,
  shares_count INT DEFAULT 0,
  views_count INT DEFAULT 0,
  favorites_count INT DEFAULT 0,
  engagement_rate DECIMAL(5,4),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (content_id) REFERENCES contents(id)
);
```

## Error Handling

### 错误分类和处理策略

#### 1. 平台访问错误
- **反爬虫限制**: 实现智能延时、IP轮换、User-Agent轮换
- **API限流**: 实现请求队列和重试机制
- **登录失效**: 自动重新登录或通知用户更新凭证

#### 2. 数据处理错误
- **解析失败**: 记录原始数据，标记为待人工处理
- **数据格式异常**: 使用默认值填充，记录异常日志
- **重复数据**: 基于内容指纹进行去重

#### 3. 系统错误
- **网络超时**: 实现指数退避重试策略
- **存储空间不足**: 自动清理过期数据，发送告警
- **服务崩溃**: 实现健康检查和自动重启

### 错误处理实现

```typescript
class ErrorHandler {
  static async handleCollectionError(error: CollectionError, context: CollectionContext) {
    switch (error.type) {
      case 'RATE_LIMIT':
        await this.handleRateLimit(error, context);
        break;
      case 'ANTI_CRAWLER':
        await this.handleAntiCrawler(error, context);
        break;
      case 'NETWORK_ERROR':
        await this.handleNetworkError(error, context);
        break;
      default:
        await this.handleUnknownError(error, context);
    }
  }
}
```

## Testing Strategy

### 测试层级

#### 1. 单元测试
- **覆盖率目标**: 80%以上
- **测试框架**: Jest + Testing Library
- **重点测试**: 数据处理逻辑、API接口、工具函数

#### 2. 集成测试
- **API测试**: 使用Supertest测试所有API端点
- **数据库测试**: 测试数据模型和查询逻辑
- **第三方服务测试**: Mock外部API调用

#### 3. 端到端测试
- **用户流程测试**: 使用Playwright测试完整用户操作流程
- **性能测试**: 使用Artillery进行负载测试
- **兼容性测试**: 测试不同浏览器和设备的兼容性

### 测试数据管理

```typescript
// 测试数据工厂
class TestDataFactory {
  static createMockContent(platform: Platform): ContentItem {
    return {
      id: faker.datatype.uuid(),
      platform,
      author: this.createMockAuthor(),
      content: this.createMockContentInfo(),
      metrics: this.createMockMetrics(),
      originalUrl: faker.internet.url(),
      collectedAt: new Date()
    };
  }
}
```

### 平台特定测试策略

#### 小红书测试
- Mock小红书API响应格式
- 测试图文混合内容解析
- 验证话题标签提取准确性

#### 抖音测试
- Mock视频元数据提取
- 测试短视频内容文本识别
- 验证用户互动数据准确性

#### 微信平台测试
- 测试公众号文章抓取可行性
- 验证视频号内容访问限制
- 测试内容更新频率检测

### 持续集成配置

```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run unit tests
        run: npm run test:unit
      - name: Run integration tests
        run: npm run test:integration
      - name: Run E2E tests
        run: npm run test:e2e
```