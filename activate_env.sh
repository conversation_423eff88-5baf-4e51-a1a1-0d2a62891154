#!/bin/bash

# AutoGen 工作流环境激活脚本

echo "🚀 激活 AutoGen 工作流虚拟环境..."

# 检查虚拟环境是否存在
if [ ! -d "autogen_workflow_env" ]; then
    echo "❌ 虚拟环境不存在，请先运行安装脚本"
    exit 1
fi

# 激活虚拟环境
source autogen_workflow_env/bin/activate

# 检查 OpenAI API Key
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  警告: OPENAI_API_KEY 环境变量未设置"
    echo "请设置您的 OpenAI API 密钥:"
    echo "export OPENAI_API_KEY='your-api-key-here'"
else
    echo "✅ OpenAI API Key 已设置"
fi

# 显示环境信息
echo "📋 环境信息:"
echo "   Python 版本: $(python --version)"
echo "   虚拟环境: $(which python)"
echo "   工作目录: $(pwd)"

# 验证 AutoGen 安装
python -c "
try:
    import autogen_agentchat
    import autogen_ext.models.openai
    print('✅ AutoGen 包验证成功')
except ImportError as e:
    print(f'❌ AutoGen 包导入失败: {e}')
"

echo ""
echo "🎯 可用的命令:"
echo "   python demo.py                    # 运行演示"
echo "   python workflow_examples.py      # 运行示例"
echo "   python test_workflow.py          # 运行测试"
echo "   deactivate                       # 退出虚拟环境"
echo ""
echo "📚 文档:"
echo "   cat AUTOGEN_WORKFLOW_README.md   # 查看详细文档"
echo "   cat WORKFLOW_SUMMARY.md          # 查看系统总结"
echo ""
echo "🎉 环境已激活，可以开始使用 AutoGen 工作流！"
