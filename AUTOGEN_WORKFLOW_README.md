# AutoGen 编程工作流

基于 Microsoft AutoGen 框架的智能编程工作流系统，实现了三个协作 AI Agent：代码编写者、代码审查者和代码优化者。

## 🌟 特性

### 核心功能
- **智能代码生成**: Agent1 负责根据需求编写高质量代码
- **专业代码审查**: Agent2 进行全面的代码质量审查
- **自动代码优化**: Agent3 基于审查建议优化代码

### 高级功能
- **代码执行验证**: 自动执行生成的代码并验证结果
- **多轮迭代优化**: 支持多轮审查和优化循环
- **结果持久化**: 自动保存工作流结果和最终代码
- **配置灵活**: 支持不同的模型和执行策略
- **交互式模式**: 提供命令行交互界面

## 🏗️ 架构设计

```mermaid
graph TD
    A[用户任务] --> B[CodeWriter Agent]
    B --> C[CodeReviewer Agent]
    C --> D{审查结果}
    D -->|APPROVED/NEEDS_IMPROVEMENT| E[CodeOptimizer Agent]
    D -->|NEEDS_REWRITE| B
    E --> F[CodeExecutor Agent]
    F --> G[最终结果]
    
    style B fill:#e1f5fe
    style C fill:#fff3e0
    style E fill:#f3e5f5
    style F fill:#e8f5e8
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 设置环境变量

```bash
export OPENAI_API_KEY="your-openai-api-key"
```

### 3. 运行基础示例

```python
import asyncio
from autogen_programming_workflow import ProgrammingWorkflow

async def main():
    workflow = ProgrammingWorkflow()
    
    task = """
    编写一个Python函数，计算斐波那契数列的第n项，
    要求使用动态规划优化性能，包含完整的类型注解和文档字符串。
    """
    
    await workflow.run_programming_task(task)
    await workflow.close()

asyncio.run(main())
```

### 4. 运行高级示例

```python
import asyncio
from advanced_autogen_workflow import AdvancedProgrammingWorkflow, WorkflowConfig

async def main():
    config = WorkflowConfig(
        model_name="gpt-4o",
        enable_code_execution=True,
        max_iterations=3
    )
    
    workflow = AdvancedProgrammingWorkflow(config)
    
    task = """
    设计一个线程安全的LRU缓存系统，支持泛型，
    包含完整的单元测试和性能基准测试。
    """
    
    result = await workflow.run_task(task)
    print(f"任务完成: {result.success}")
    
    await workflow.close()

asyncio.run(main())
```

## 📋 使用示例

### 交互式模式

```bash
python workflow_examples.py
```

### 批量运行示例

```python
from workflow_examples import run_batch_examples

# 运行所有示例任务
results = await run_batch_examples()

# 运行特定任务
results = await run_batch_examples(['数据结构实现', '算法优化'])
```

## ⚙️ 配置选项

### WorkflowConfig 参数

```python
@dataclass
class WorkflowConfig:
    model_name: str = "gpt-4o"              # 使用的模型
    max_iterations: int = 3                  # 最大迭代次数
    enable_code_execution: bool = True       # 是否启用代码执行
    enable_testing: bool = True              # 是否启用测试生成
    output_dir: str = "workflow_output"      # 输出目录
    temperature: float = 0.1                 # 模型温度参数
```

## 📊 输出结果

工作流会生成以下输出：

### 1. 任务结果 JSON
```json
{
  "task_id": "task_20241223_143022",
  "description": "任务描述",
  "final_code": "最终优化后的代码",
  "review_history": ["审查记录1", "审查记录2"],
  "optimization_history": ["优化记录1"],
  "execution_results": ["执行结果"],
  "timestamp": "2024-12-23T14:30:22",
  "success": true
}
```

### 2. 最终代码文件
```python
# task_20241223_143022_final_code.py
def fibonacci(n: int) -> int:
    """计算斐波那契数列的第n项"""
    # 优化后的代码...
```

## 🔧 高级用法

### 1. 自定义工作流图

```python
from autogen_agentchat.teams import DiGraphBuilder

builder = DiGraphBuilder()
builder.add_node(code_writer)
builder.add_node(code_reviewer)
builder.add_node(code_optimizer)

# 添加条件边
builder.add_edge(
    code_reviewer, 
    code_optimizer,
    condition=lambda msg: "APPROVED" in msg.content
)
```

### 2. 添加自定义 Agent

```python
custom_agent = AssistantAgent(
    name="TestGenerator",
    model_client=model_client,
    system_message="专门生成单元测试的Agent..."
)

builder.add_node(custom_agent)
builder.add_edge(code_optimizer, custom_agent)
```

## 🧪 测试

运行单元测试：

```bash
pytest tests/ -v
```

## 📈 性能优化

### 1. 模型选择
- **快速原型**: `gpt-4o-mini`
- **高质量**: `gpt-4o`
- **成本优化**: `gpt-3.5-turbo`

### 2. 并发执行
```python
# 并行运行多个任务
tasks = [task1, task2, task3]
results = await asyncio.gather(*[
    workflow.run_task(task) for task in tasks
])
```

## 📞 支持

如有问题或建议，请创建 Issue 或参与 Discussions。

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
